#!/bin/bash

echo "🚀 Starting WITHOUT Ground Truth Experiments..."

methods=("all_at_once" "step_by_step" "binary_search")
model="gpt-4o"

# Create output directory for without ground truth experiments
mkdir -p "outputs/without_gt"

for method in "${methods[@]}"; do
    echo "🔄 Running $method (WITHOUT Ground Truth)..."
    
    # Handcrafted
    echo "  📁 Processing Handcrafted data..."
    python3 inference_cloudgpt_v2.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../Who&When/Hand-Crafted" --output_dir "outputs/without_gt"
    
    # Algorithm-generated  
    echo "  📁 Processing Algorithm-Generated data..."
    python3 inference_cloudgpt_v2.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../Who&When/Algorithm-Generated" --output_dir "outputs/without_gt"
    
    echo "  ✅ $method completed!"
done

echo "🎉 All WITHOUT Ground Truth experiments completed!"
echo "📁 Results saved in: outputs/without_gt/"
echo ""
echo "Generated files:"
ls -la outputs/without_gt/
