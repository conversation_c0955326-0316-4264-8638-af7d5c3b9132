--- Starting Analysis: all_at_once (WITHOUT Ground Truth) ---
Timestamp: 2025-08-03 07:15:38.684990
Model Family: gpt
Model Used: gpt-4o
Input Directory: ../Who&When/Hand-Crafted
Is Handcrafted: True
Ground Truth: DISABLED
--------------------------------------------------

--- Starting All-at-Once Analysis (WITHOUT Ground Truth) ---

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 1.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 2.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 3.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 4.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 5.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 6.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 7.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 8.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 9.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 10.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 11.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 12.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 13.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 14.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 15.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 16.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 17.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 18.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 19.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 20.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 21.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 22.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 23.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 24.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 25.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 26.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 27.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 28.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 29.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 30.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 31.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 32.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 33.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 34.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 35.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 36.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 37.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 38.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 39.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 40.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 41.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 42.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 43.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 44.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 45.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 46.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 47.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 48.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 49.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 50.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 51.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 52.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 53.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 54.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 55.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 56.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 57.json:
Failed to get prediction.

==================================================

Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
Prediction for 58.json:
Failed to get prediction.

==================================================

--------------------
--- Analysis Complete ---
