--- Starting Analysis: binary_search (WITHOUT Ground Truth) ---
Timestamp: 2025-08-03 07:16:40.189159
Model Family: gpt
Model Used: gpt-4o
Input Directory: ../Who&When/Hand-Crafted
Is Handcrafted: True
Ground Truth: DISABLED
--------------------------------------------------

--- Starting Binary Search Analysis (WITHOUT Ground Truth) ---

--- Analyzing File: 1.json ---
Analyzing step 0-28 for 1.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-28. Stopping binary search for 1.json.
--- Analyzing File: 2.json ---
Analyzing step 0-85 for 2.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-85. Stopping binary search for 2.json.
--- Analyzing File: 3.json ---
Analyzing step 0-92 for 3.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-92. Stopping binary search for 3.json.
--- Analyzing File: 4.json ---
Analyzing step 0-16 for 4.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-16. Stopping binary search for 4.json.
--- Analyzing File: 5.json ---
Analyzing step 0-19 for 5.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-19. Stopping binary search for 5.json.
--- Analyzing File: 6.json ---
Analyzing step 0-7 for 6.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 6.json.
--- Analyzing File: 7.json ---
Analyzing step 0-24 for 7.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-24. Stopping binary search for 7.json.
--- Analyzing File: 8.json ---
Analyzing step 0-128 for 8.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-128. Stopping binary search for 8.json.
--- Analyzing File: 9.json ---
Analyzing step 0-94 for 9.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-94. Stopping binary search for 9.json.
--- Analyzing File: 10.json ---
Analyzing step 0-43 for 10.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-43. Stopping binary search for 10.json.
--- Analyzing File: 11.json ---
Analyzing step 0-129 for 11.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-129. Stopping binary search for 11.json.
--- Analyzing File: 12.json ---
Analyzing step 0-19 for 12.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-19. Stopping binary search for 12.json.
--- Analyzing File: 13.json ---
Analyzing step 0-52 for 13.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-52. Stopping binary search for 13.json.
--- Analyzing File: 14.json ---
Analyzing step 0-31 for 14.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-31. Stopping binary search for 14.json.
--- Analyzing File: 15.json ---
Analyzing step 0-128 for 15.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-128. Stopping binary search for 15.json.
--- Analyzing File: 16.json ---
Analyzing step 0-20 for 16.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-20. Stopping binary search for 16.json.
--- Analyzing File: 17.json ---
Analyzing step 0-36 for 17.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-36. Stopping binary search for 17.json.
--- Analyzing File: 18.json ---
Analyzing step 0-30 for 18.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-30. Stopping binary search for 18.json.
--- Analyzing File: 19.json ---
Analyzing step 0-68 for 19.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-68. Stopping binary search for 19.json.
--- Analyzing File: 20.json ---
Analyzing step 0-66 for 20.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-66. Stopping binary search for 20.json.
--- Analyzing File: 21.json ---
Analyzing step 0-24 for 21.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-24. Stopping binary search for 21.json.
--- Analyzing File: 22.json ---
Analyzing step 0-23 for 22.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-23. Stopping binary search for 22.json.
--- Analyzing File: 23.json ---
Analyzing step 0-73 for 23.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-73. Stopping binary search for 23.json.
--- Analyzing File: 24.json ---
Analyzing step 0-4 for 24.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 24.json.
--- Analyzing File: 25.json ---
Analyzing step 0-19 for 25.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-19. Stopping binary search for 25.json.
--- Analyzing File: 26.json ---
Analyzing step 0-32 for 26.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-32. Stopping binary search for 26.json.
--- Analyzing File: 27.json ---
Analyzing step 0-50 for 27.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-50. Stopping binary search for 27.json.
--- Analyzing File: 28.json ---
Analyzing step 0-31 for 28.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-31. Stopping binary search for 28.json.
--- Analyzing File: 29.json ---
Analyzing step 0-12 for 29.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-12. Stopping binary search for 29.json.
--- Analyzing File: 30.json ---
Analyzing step 0-120 for 30.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-120. Stopping binary search for 30.json.
--- Analyzing File: 31.json ---
Analyzing step 0-31 for 31.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-31. Stopping binary search for 31.json.
--- Analyzing File: 32.json ---
Analyzing step 0-11 for 32.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-11. Stopping binary search for 32.json.
--- Analyzing File: 33.json ---
Analyzing step 0-8 for 33.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 33.json.
--- Analyzing File: 34.json ---
Analyzing step 0-4 for 34.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 34.json.
--- Analyzing File: 35.json ---
Analyzing step 0-43 for 35.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-43. Stopping binary search for 35.json.
--- Analyzing File: 36.json ---
Analyzing step 0-90 for 36.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-90. Stopping binary search for 36.json.
--- Analyzing File: 37.json ---
Analyzing step 0-58 for 37.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-58. Stopping binary search for 37.json.
--- Analyzing File: 38.json ---
Analyzing step 0-51 for 38.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-51. Stopping binary search for 38.json.
--- Analyzing File: 39.json ---
Analyzing step 0-48 for 39.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-48. Stopping binary search for 39.json.
--- Analyzing File: 40.json ---
Analyzing step 0-16 for 40.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-16. Stopping binary search for 40.json.
--- Analyzing File: 41.json ---
Analyzing step 0-82 for 41.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-82. Stopping binary search for 41.json.
--- Analyzing File: 42.json ---
Analyzing step 0-31 for 42.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-31. Stopping binary search for 42.json.
--- Analyzing File: 43.json ---
Analyzing step 0-15 for 43.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-15. Stopping binary search for 43.json.
--- Analyzing File: 44.json ---
Analyzing step 0-123 for 44.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-123. Stopping binary search for 44.json.
--- Analyzing File: 45.json ---
Analyzing step 0-20 for 45.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-20. Stopping binary search for 45.json.
--- Analyzing File: 46.json ---
Analyzing step 0-129 for 46.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-129. Stopping binary search for 46.json.
--- Analyzing File: 47.json ---
Analyzing step 0-66 for 47.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-66. Stopping binary search for 47.json.
--- Analyzing File: 48.json ---
Analyzing step 0-4 for 48.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 48.json.
--- Analyzing File: 49.json ---
Analyzing step 0-15 for 49.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-15. Stopping binary search for 49.json.
--- Analyzing File: 50.json ---
Analyzing step 0-112 for 50.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-112. Stopping binary search for 50.json.
--- Analyzing File: 51.json ---
Analyzing step 0-122 for 51.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-122. Stopping binary search for 51.json.
--- Analyzing File: 52.json ---
Analyzing step 0-20 for 52.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-20. Stopping binary search for 52.json.
--- Analyzing File: 53.json ---
Analyzing step 0-27 for 53.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-27. Stopping binary search for 53.json.
--- Analyzing File: 54.json ---
Analyzing step 0-18 for 54.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-18. Stopping binary search for 54.json.
--- Analyzing File: 55.json ---
Analyzing step 0-39 for 55.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-39. Stopping binary search for 55.json.
--- Analyzing File: 56.json ---
Analyzing step 0-128 for 56.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-128. Stopping binary search for 56.json.
--- Analyzing File: 57.json ---
Analyzing step 0-16 for 57.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-16. Stopping binary search for 57.json.
--- Analyzing File: 58.json ---
Analyzing step 0-105 for 58.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-105. Stopping binary search for 58.json.
--------------------
--- Analysis Complete ---
