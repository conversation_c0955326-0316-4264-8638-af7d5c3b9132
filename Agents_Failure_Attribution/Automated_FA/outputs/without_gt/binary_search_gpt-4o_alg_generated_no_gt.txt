--- Starting Analysis: binary_search (WITHOUT Ground Truth) ---
Timestamp: 2025-08-03 07:16:52.332519
Model Family: gpt
Model Used: gpt-4o
Input Directory: ../Who&When/Algorithm-Generated
Is Handcrafted: False
Ground Truth: DISABLED
--------------------------------------------------

--- Starting Binary Search Analysis (WITHOUT Ground Truth) ---

--- Analyzing File: 1.json ---
Analyzing step 0-5 for 1.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 1.json.
--- Analyzing File: 2.json ---
Analyzing step 0-6 for 2.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 2.json.
--- Analyzing File: 3.json ---
Analyzing step 0-7 for 3.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 3.json.
--- Analyzing File: 4.json ---
Analyzing step 0-7 for 4.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 4.json.
--- Analyzing File: 5.json ---
Analyzing step 0-8 for 5.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 5.json.
--- Analyzing File: 6.json ---
Analyzing step 0-7 for 6.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 6.json.
--- Analyzing File: 7.json ---
Analyzing step 0-9 for 7.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 7.json.
--- Analyzing File: 8.json ---
Analyzing step 0-9 for 8.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 8.json.
--- Analyzing File: 9.json ---
Analyzing step 0-4 for 9.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 9.json.
--- Analyzing File: 10.json ---
Analyzing step 0-9 for 10.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 10.json.
--- Analyzing File: 11.json ---
Analyzing step 0-9 for 11.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 11.json.
--- Analyzing File: 12.json ---
Analyzing step 0-4 for 12.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 12.json.
--- Analyzing File: 13.json ---
Analyzing step 0-9 for 13.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 13.json.
--- Analyzing File: 14.json ---
Analyzing step 0-9 for 14.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 14.json.
--- Analyzing File: 15.json ---
Analyzing step 0-9 for 15.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 15.json.
--- Analyzing File: 16.json ---
Analyzing step 0-9 for 16.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 16.json.
--- Analyzing File: 17.json ---
Analyzing step 0-9 for 17.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 17.json.
--- Analyzing File: 18.json ---
Analyzing step 0-9 for 18.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 18.json.
--- Analyzing File: 19.json ---
Analyzing step 0-9 for 19.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 19.json.
--- Analyzing File: 20.json ---
Analyzing step 0-9 for 20.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 20.json.
--- Analyzing File: 21.json ---
Analyzing step 0-5 for 21.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 21.json.
--- Analyzing File: 22.json ---
Analyzing step 0-5 for 22.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 22.json.
--- Analyzing File: 23.json ---
Analyzing step 0-9 for 23.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 23.json.
--- Analyzing File: 24.json ---
Analyzing step 0-9 for 24.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 24.json.
--- Analyzing File: 25.json ---
Analyzing step 0-9 for 25.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 25.json.
--- Analyzing File: 26.json ---
Analyzing step 0-8 for 26.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 26.json.
--- Analyzing File: 27.json ---
Analyzing step 0-9 for 27.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 27.json.
--- Analyzing File: 28.json ---
Analyzing step 0-9 for 28.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 28.json.
--- Analyzing File: 29.json ---
Analyzing step 0-9 for 29.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 29.json.
--- Analyzing File: 30.json ---
Analyzing step 0-6 for 30.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 30.json.
--- Analyzing File: 31.json ---
Analyzing step 0-9 for 31.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 31.json.
--- Analyzing File: 32.json ---
Analyzing step 0-9 for 32.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 32.json.
--- Analyzing File: 33.json ---
Analyzing step 0-9 for 33.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 33.json.
--- Analyzing File: 34.json ---
Analyzing step 0-7 for 34.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 34.json.
--- Analyzing File: 35.json ---
Analyzing step 0-9 for 35.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 35.json.
--- Analyzing File: 36.json ---
Analyzing step 0-8 for 36.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 36.json.
--- Analyzing File: 37.json ---
Analyzing step 0-5 for 37.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 37.json.
--- Analyzing File: 38.json ---
Analyzing step 0-5 for 38.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 38.json.
--- Analyzing File: 39.json ---
Analyzing step 0-9 for 39.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 39.json.
--- Analyzing File: 40.json ---
Analyzing step 0-8 for 40.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 40.json.
--- Analyzing File: 41.json ---
Analyzing step 0-5 for 41.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 41.json.
--- Analyzing File: 42.json ---
Analyzing step 0-5 for 42.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 42.json.
--- Analyzing File: 43.json ---
Analyzing step 0-8 for 43.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 43.json.
--- Analyzing File: 44.json ---
Analyzing step 0-9 for 44.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 44.json.
--- Analyzing File: 45.json ---
Analyzing step 0-4 for 45.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 45.json.
--- Analyzing File: 46.json ---
Analyzing step 0-6 for 46.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 46.json.
--- Analyzing File: 47.json ---
Analyzing step 0-5 for 47.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 47.json.
--- Analyzing File: 48.json ---
Analyzing step 0-9 for 48.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 48.json.
--- Analyzing File: 49.json ---
Analyzing step 0-9 for 49.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 49.json.
--- Analyzing File: 50.json ---
Analyzing step 0-9 for 50.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 50.json.
--- Analyzing File: 51.json ---
Analyzing step 0-5 for 51.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 51.json.
--- Analyzing File: 52.json ---
Analyzing step 0-9 for 52.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 52.json.
--- Analyzing File: 53.json ---
Analyzing step 0-4 for 53.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 53.json.
--- Analyzing File: 54.json ---
Analyzing step 0-9 for 54.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 54.json.
--- Analyzing File: 55.json ---
Analyzing step 0-9 for 55.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 55.json.
--- Analyzing File: 56.json ---
Analyzing step 0-9 for 56.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 56.json.
--- Analyzing File: 57.json ---
Analyzing step 0-4 for 57.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-4. Stopping binary search for 57.json.
--- Analyzing File: 58.json ---
Analyzing step 0-5 for 58.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 58.json.
--- Analyzing File: 59.json ---
Analyzing step 0-9 for 59.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 59.json.
--- Analyzing File: 60.json ---
Analyzing step 0-9 for 60.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 60.json.
--- Analyzing File: 61.json ---
Analyzing step 0-9 for 61.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 61.json.
--- Analyzing File: 62.json ---
Analyzing step 0-6 for 62.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 62.json.
--- Analyzing File: 63.json ---
Analyzing step 0-9 for 63.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 63.json.
--- Analyzing File: 64.json ---
Analyzing step 0-9 for 64.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 64.json.
--- Analyzing File: 65.json ---
Analyzing step 0-5 for 65.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 65.json.
--- Analyzing File: 66.json ---
Analyzing step 0-5 for 66.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 66.json.
--- Analyzing File: 67.json ---
Analyzing step 0-9 for 67.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 67.json.
--- Analyzing File: 68.json ---
Analyzing step 0-6 for 68.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 68.json.
--- Analyzing File: 69.json ---
Analyzing step 0-9 for 69.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 69.json.
--- Analyzing File: 70.json ---
Analyzing step 0-5 for 70.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 70.json.
--- Analyzing File: 71.json ---
Analyzing step 0-9 for 71.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 71.json.
--- Analyzing File: 72.json ---
Analyzing step 0-9 for 72.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 72.json.
--- Analyzing File: 73.json ---
Analyzing step 0-6 for 73.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 73.json.
--- Analyzing File: 74.json ---
Analyzing step 0-9 for 74.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 74.json.
--- Analyzing File: 75.json ---
Analyzing step 0-6 for 75.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 75.json.
--- Analyzing File: 76.json ---
Analyzing step 0-9 for 76.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 76.json.
--- Analyzing File: 77.json ---
Analyzing step 0-9 for 77.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 77.json.
--- Analyzing File: 78.json ---
Analyzing step 0-9 for 78.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 78.json.
--- Analyzing File: 79.json ---
Analyzing step 0-9 for 79.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 79.json.
--- Analyzing File: 80.json ---
Analyzing step 0-7 for 80.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 80.json.
--- Analyzing File: 81.json ---
Analyzing step 0-8 for 81.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 81.json.
--- Analyzing File: 82.json ---
Analyzing step 0-6 for 82.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 82.json.
--- Analyzing File: 83.json ---
Analyzing step 0-9 for 83.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 83.json.
--- Analyzing File: 84.json ---
Analyzing step 0-8 for 84.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 84.json.
--- Analyzing File: 85.json ---
Analyzing step 0-8 for 85.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 85.json.
--- Analyzing File: 86.json ---
Analyzing step 0-6 for 86.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 86.json.
--- Analyzing File: 87.json ---
Analyzing step 0-8 for 87.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 87.json.
--- Analyzing File: 88.json ---
Analyzing step 0-9 for 88.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 88.json.
--- Analyzing File: 89.json ---
Analyzing step 0-9 for 89.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 89.json.
--- Analyzing File: 90.json ---
Analyzing step 0-9 for 90.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 90.json.
--- Analyzing File: 91.json ---
Analyzing step 0-9 for 91.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 91.json.
--- Analyzing File: 92.json ---
Analyzing step 0-9 for 92.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 92.json.
--- Analyzing File: 93.json ---
Analyzing step 0-5 for 93.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 93.json.
--- Analyzing File: 94.json ---
Analyzing step 0-9 for 94.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 94.json.
--- Analyzing File: 95.json ---
Analyzing step 0-9 for 95.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 95.json.
--- Analyzing File: 96.json ---
Analyzing step 0-9 for 96.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 96.json.
--- Analyzing File: 97.json ---
Analyzing step 0-9 for 97.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 97.json.
--- Analyzing File: 98.json ---
Analyzing step 0-6 for 98.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 98.json.
--- Analyzing File: 99.json ---
Analyzing step 0-6 for 99.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 99.json.
--- Analyzing File: 100.json ---
Analyzing step 0-9 for 100.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 100.json.
--- Analyzing File: 101.json ---
Analyzing step 0-7 for 101.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 101.json.
--- Analyzing File: 102.json ---
Analyzing step 0-9 for 102.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 102.json.
--- Analyzing File: 103.json ---
Analyzing step 0-9 for 103.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 103.json.
--- Analyzing File: 104.json ---
Analyzing step 0-9 for 104.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 104.json.
--- Analyzing File: 105.json ---
Analyzing step 0-7 for 105.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 105.json.
--- Analyzing File: 106.json ---
Analyzing step 0-5 for 106.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-5. Stopping binary search for 106.json.
--- Analyzing File: 107.json ---
Analyzing step 0-7 for 107.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 107.json.
--- Analyzing File: 108.json ---
Analyzing step 0-9 for 108.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 108.json.
--- Analyzing File: 109.json ---
Analyzing step 0-9 for 109.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 109.json.
--- Analyzing File: 110.json ---
Analyzing step 0-8 for 110.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 110.json.
--- Analyzing File: 111.json ---
Analyzing step 0-9 for 111.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 111.json.
--- Analyzing File: 112.json ---
Analyzing step 0-9 for 112.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 112.json.
--- Analyzing File: 113.json ---
Analyzing step 0-8 for 113.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-8. Stopping binary search for 113.json.
--- Analyzing File: 114.json ---
Analyzing step 0-6 for 114.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 114.json.
--- Analyzing File: 115.json ---
Analyzing step 0-6 for 115.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 115.json.
--- Analyzing File: 116.json ---
Analyzing step 0-9 for 116.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 116.json.
--- Analyzing File: 117.json ---
Analyzing step 0-6 for 117.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 117.json.
--- Analyzing File: 118.json ---
Analyzing step 0-9 for 118.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 118.json.
--- Analyzing File: 119.json ---
Analyzing step 0-7 for 119.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 119.json.
--- Analyzing File: 120.json ---
Analyzing step 0-9 for 120.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 120.json.
--- Analyzing File: 121.json ---
Analyzing step 0-6 for 121.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-6. Stopping binary search for 121.json.
--- Analyzing File: 122.json ---
Analyzing step 0-9 for 122.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 122.json.
--- Analyzing File: 123.json ---
Analyzing step 0-9 for 123.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 123.json.
--- Analyzing File: 124.json ---
Analyzing step 0-9 for 124.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 124.json.
--- Analyzing File: 125.json ---
Analyzing step 0-9 for 125.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-9. Stopping binary search for 125.json.
--- Analyzing File: 126.json ---
Analyzing step 0-7 for 126.json...
Error during OpenAI API call: Error code: 401 - {'statusCode': 401, 'message': 'Unauthorized. Access token is missing or invalid.'}
API call failed for segment 0-7. Stopping binary search for 126.json.
--------------------
--- Analysis Complete ---
